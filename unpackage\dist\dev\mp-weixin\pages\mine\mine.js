"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_userStore = require("../../utils/user-store.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  __name: "mine",
  setup(__props) {
    const isLoading = common_vendor.ref(false);
    const wechatAvatarUrl = common_vendor.ref("");
    const wechatNickname = common_vendor.ref("");
    const wechatOpenid = common_vendor.ref("");
    const isStaff = common_vendor.ref(false);
    common_vendor.computed(() => utils_userStore.userStore.getUserState());
    const isLoggedIn = utils_userStore.userComputed.isLoggedIn;
    const displayName = common_vendor.computed(() => {
      return wechatNickname.value || utils_userStore.userComputed.displayName.value || utils_userStore.userComputed.wechatUserInfo.nickName || "用户";
    });
    const displayAvatar = common_vendor.computed(() => {
      return wechatAvatarUrl.value || utils_userStore.userComputed.displayAvatar.value || utils_userStore.userComputed.wechatUserInfo.avatarUrl || "/static/default-avatar.png";
    });
    const authStatus = common_vendor.computed(() => {
      if (!isLoggedIn.value)
        return "not_logged_in";
      return isStaff.value ? "authenticated" : "need_auth";
    });
    const authStatusText = common_vendor.computed(() => {
      switch (authStatus.value) {
        case "authenticated":
          return "已完成实名认证";
        case "need_auth":
          return "请完成实名认证以使用完整功能";
        default:
          return "未登录";
      }
    });
    common_vendor.onMounted(() => {
      const storedOpenid = common_vendor.index.getStorageSync("wechat_openid");
      if (storedOpenid) {
        wechatOpenid.value = storedOpenid;
      }
      const storedIsStaff = common_vendor.index.getStorageSync("user_is_staff");
      if (typeof storedIsStaff !== "undefined") {
        isStaff.value = storedIsStaff;
      }
      checkLoginAndGetUserInfo();
    });
    async function checkLoginAndGetUserInfo() {
      if (!isLoggedIn.value) {
        isLoading.value = false;
        return;
      }
      try {
        isLoading.value = true;
        const result = await utils_api.api.user.getUserInfo();
        if (result.success && result.data) {
          if (result.data.wechat_avatar_url) {
            wechatAvatarUrl.value = result.data.wechat_avatar_url;
          }
          if (result.data.wechat_nickname) {
            wechatNickname.value = result.data.wechat_nickname;
          }
          if (result.data.wechat_openid) {
            wechatOpenid.value = result.data.wechat_openid;
            common_vendor.index.setStorageSync("wechat_openid", result.data.wechat_openid);
          }
          if (typeof result.data.is_staff !== "undefined") {
            isStaff.value = result.data.is_staff;
            common_vendor.index.setStorageSync("user_is_staff", result.data.is_staff);
          }
          utils_userStore.userStore.updateUserInfo(result.data);
          recordOperationLog("查看", "个人中心");
        } else {
          common_vendor.index.__f__("warn", "at pages/mine/mine.vue:220", "获取用户信息失败:", result.message || "未知错误");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mine/mine.vue:223", "获取用户信息失败:", error);
        if (error.message && error.message.includes("401")) {
          utils_userStore.userStore.clearUserInfo();
          common_vendor.index.showModal({
            title: "登录过期",
            content: "您的登录已过期，请重新登录",
            showCancel: false,
            success: () => {
              common_vendor.index.navigateTo({
                url: "/pages/login/login"
              });
            }
          });
        }
      } finally {
        isLoading.value = false;
      }
    }
    function handleAvatarError() {
      common_vendor.index.__f__("log", "at pages/mine/mine.vue:248", "头像加载失败，使用默认头像");
    }
    async function recordOperationLog(action, target) {
      try {
        if (!isLoggedIn.value)
          return;
        await utils_api.api.operationLog.recordOperation({
          button_name: action,
          // 按钮名称
          button_type: target,
          // 按钮类型
          page_url: getCurrentPages()[getCurrentPages().length - 1].route,
          // 浏览器路径
          page_plate: "个人中心"
          // 菜单名称
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mine/mine.vue:263", "记录操作日志失败:", error);
      }
    }
    function navigateTo(url) {
      if (!isLoggedIn.value && url !== "/pages/login/login") {
        common_vendor.index.navigateTo({
          url: "/pages/login/login"
        });
        return;
      }
      const pageName = getPageNameFromUrl(url);
      recordOperationLog("点击", pageName);
      common_vendor.index.navigateTo({
        url
      });
    }
    function getPageNameFromUrl(url) {
      const pageMap = {
        "/pages/mediation_query/mediation_query": "我的调解记录",
        "/pages/privacy_policy/privacy_policy": "隐私政策",
        "/pages/login/login": "登录页面"
      };
      return pageMap[url] || "未知页面";
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isLoading.value
      }, isLoading.value ? {} : common_vendor.e({
        b: displayAvatar.value,
        c: common_vendor.o(handleAvatarError),
        d: common_vendor.t(common_vendor.unref(isLoggedIn) ? displayName.value : "点击登录"),
        e: common_vendor.o(($event) => !common_vendor.unref(isLoggedIn) && navigateTo("/pages/login/login")),
        f: common_vendor.unref(isLoggedIn)
      }, common_vendor.unref(isLoggedIn) ? common_vendor.e({
        g: authStatus.value === "authenticated"
      }, authStatus.value === "authenticated" ? {} : {}, {
        h: common_vendor.t(authStatusText.value),
        i: common_vendor.n(authStatus.value)
      }) : {}), {
        j: !common_vendor.unref(isLoggedIn) && !isLoading.value
      }, !common_vendor.unref(isLoggedIn) && !isLoading.value ? {
        k: common_vendor.o(($event) => navigateTo("/pages/login/login")),
        l: isLoading.value
      } : {}, {
        m: common_vendor.unref(isLoggedIn) || authStatus.value === "authenticated"
      }, common_vendor.unref(isLoggedIn) || authStatus.value === "authenticated" ? {
        n: common_vendor.o(($event) => navigateTo("/pages/mediation_query/mediation_query"))
      } : {}, {
        o: common_vendor.o(($event) => navigateTo("/pages/privacy_policy/privacy_policy"))
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7c2ebfa5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mine/mine.js.map
