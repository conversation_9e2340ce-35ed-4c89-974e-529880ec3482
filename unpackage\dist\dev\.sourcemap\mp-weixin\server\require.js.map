{"version": 3, "file": "require.js", "sources": ["server/require.js"], "sourcesContent": ["// HTTP请求拦截器模块\r\nimport { getBaseURL, getTimeout, isDebug } from '@/config/env.js';\r\n\r\n// 全局token刷新状态管理\r\nlet isRefreshing = false;\r\nlet refreshPromise = null;\r\nconst waitingRequests = [];\r\n\r\n// 内存缓存优化\r\nlet tokenCacheTimer = null;\r\nlet lastTokenCheck = 0;\r\nconst TOKEN_CHECK_INTERVAL = 5000; // 5秒内最多检查一次token\r\n\r\n/**\r\n * 获取token的统一函数\r\n * 支持从本地存储和URL参数获取token，优先使用access_token\r\n */\r\nexport const getToken = () => {\r\n  // 优先从本地存储获取access_token\r\n  let token = uni.getStorageSync('access_token');\r\n\r\n  // 如果没有access_token，尝试获取旧的token字段（向后兼容）\r\n  if (!token) {\r\n    token = uni.getStorageSync('token');\r\n  }\r\n\r\n  // 如果本地存储都没有，尝试从URL参数获取\r\n  if (!token) {\r\n    try {\r\n      const pages = getCurrentPages();\r\n      if (pages && pages.length > 0) {\r\n        const currentPage = pages[pages.length - 1];\r\n        token = currentPage.options?.token || '';\r\n\r\n        // 如果从URL获取到token，保存到本地存储\r\n        if (token) {\r\n          uni.setStorageSync('access_token', token);\r\n          console.log('从URL参数获取token并保存到本地存储');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log('从URL获取token失败:', error);\r\n    }\r\n  }\r\n\r\n  return token;\r\n};\r\n\r\n/**\r\n * 获取token类型\r\n * 默认返回Bearer\r\n */\r\nexport const getTokenType = () => {\r\n  return uni.getStorageSync('token_type') || 'Bearer';\r\n};\r\n\r\n/**\r\n * 不需要Authorization头部的接口列表\r\n * 这些接口会跳过自动添加Authorization头部\r\n */\r\nconst NO_AUTH_APIS = [\r\n  '/user/login',                    // 普通登录\r\n  '/wechat/login/',           // 微信登录\r\n  '/user/phone-login',             // 手机号登录\r\n  '/user/send-code',               // 发送验证码\r\n  '/wechat/refresh/',         // Token刷新（使用refresh_token）\r\n  '/home/<USER>',               // 首页数据（可能不需要认证）\r\n  '/case_display/case_display/',   // 案例展示（可能不需要认证）\r\n];\r\n\r\n/**\r\n * 检查接口是否需要跳过Authorization头部\r\n */\r\nconst shouldSkipAuth = (url) => {\r\n  return NO_AUTH_APIS.some(pattern => {\r\n    // 支持精确匹配和前缀匹配\r\n    return url === pattern || url.startsWith(pattern);\r\n  });\r\n};\r\n\r\n/**\r\n * 请求拦截器 - 增强版本\r\n * 自动为请求添加Authorization头，支持跳过指定接口\r\n */\r\nexport const requestInterceptor = (config) => {\r\n  // 检查是否需要跳过Authorization\r\n  const skipAuth = config.skipAuth || shouldSkipAuth(config.url);\r\n  \r\n  if (skipAuth) {\r\n    console.log(`接口 ${config.url} 跳过Authorization头部`);\r\n    return config;\r\n  }\r\n\r\n  // 获取token和token类型\r\n  const token = getToken();\r\n  const tokenType = getTokenType();\r\n\r\n  if (token) {\r\n    config.header = {\r\n      ...config.header,\r\n      'Authorization': `${tokenType} ${token}`\r\n    };\r\n    console.log(`已为请求添加Authorization头: ${tokenType} ${token.substring(0, 10)}...`);\r\n  } else {\r\n    console.warn(`接口 ${config.url} 未找到token，请求将不包含Authorization头`);\r\n  }\r\n\r\n  return config;\r\n};\r\n\r\n/**\r\n * 响应拦截器\r\n * 统一处理响应状态和错误，支持token刷新\r\n */\r\nexport const responseInterceptor = (response) => {\r\n  // 处理401未授权状态\r\n  if (response.statusCode === 401) {\r\n    // token过期或未登录\r\n    uni.showToast({\r\n      title: '登录状态已过期，请重新登录',\r\n      icon: 'none'\r\n    });\r\n\r\n    // 清除所有登录相关状态\r\n    const keysToRemove = [\r\n      'token', 'access_token', 'token_type', 'token_expire_time', 'refresh_token',\r\n      'userInfo', 'openid', 'unionid', 'sessionKey', 'wechat_userInfo'\r\n    ];\r\n    keysToRemove.forEach(key => {\r\n      uni.removeStorageSync(key);\r\n    });\r\n\r\n    // 跳转到登录页\r\n    setTimeout(() => {\r\n      uni.navigateTo({\r\n        url: '/pages/login/login'\r\n      });\r\n    }, 1500);\r\n\r\n    return Promise.reject(new Error('登录状态已过期，请重新登录'));\r\n  }\r\n\r\n  // 处理其他错误状态\r\n  if (response.statusCode !== 200) {\r\n    uni.showToast({\r\n      title: response.data.message || '请求失败',\r\n      icon: 'none'\r\n    });\r\n    return Promise.reject(new Error(response.data.message || '请求失败'));\r\n  }\r\n\r\n  return response.data;\r\n};\r\n\r\n/**\r\n * 创建带有特定配置的请求\r\n * @param {Object} options 请求配置\r\n * @param {boolean} options.skipAuth 是否跳过Authorization头部\r\n * @returns {Function} 请求函数\r\n */\r\nexport const createRequest = (defaultOptions = {}) => {\r\n  return (options) => {\r\n    return request({\r\n      ...defaultOptions,\r\n      ...options\r\n    });\r\n  };\r\n};\r\n\r\n/**\r\n * 封装的请求方法\r\n * 集成请求和响应拦截器，支持自动token刷新\r\n */\r\nexport const request = (options) => {\r\n  return new Promise(async (resolve, reject) => {\r\n    try {\r\n      // 1. 检查token有效性（带缓存优化）\r\n      const isTokenValid = authUtils.checkTokenValidityWithCache();\r\n      \r\n      // 2. 如果token无效或即将过期，尝试刷新\r\n      if (!isTokenValid && authUtils.getRefreshToken()) {\r\n        if (isRefreshing) {\r\n          // 如果正在刷新，将请求加入等待队列\r\n          authUtils.addRequestToQueue(options, resolve, reject);\r\n          return;\r\n        }\r\n\r\n        try {\r\n          // 执行token刷新\r\n          await authUtils.refreshToken();\r\n          // 刷新成功后处理等待队列\r\n          authUtils.processWaitingRequests();\r\n        } catch (refreshError) {\r\n          // 刷新失败，处理等待队列并返回错误\r\n          authUtils.processWaitingRequests(refreshError);\r\n          reject(refreshError);\r\n          return;\r\n        }\r\n      }\r\n\r\n      // 3. 应用请求拦截器（添加Authorization头等）\r\n      const config = requestInterceptor(options);\r\n      \r\n      // 4. 发送请求\r\n      uni.request({\r\n        ...config,\r\n        url: `${getBaseURL()}${config.url}`,\r\n        timeout: getTimeout(),\r\n        success: (res) => {\r\n          try {\r\n            // 5. 应用响应拦截器\r\n            const response = responseInterceptor(res);\r\n            resolve(response);\r\n          } catch (error) {\r\n            // 6. 如果响应拦截器中遇到401错误，且还没有尝试过刷新，则尝试刷新token\r\n            if (res.statusCode === 401 && !isRefreshing && authUtils.getRefreshToken()) {\r\n              // 将当前请求加入队列，然后刷新token\r\n              authUtils.addRequestToQueue(options, resolve, reject);\r\n              \r\n              authUtils.refreshToken()\r\n                .then(() => {\r\n                  // 刷新成功，处理等待队列\r\n                  authUtils.processWaitingRequests();\r\n                })\r\n                .catch((refreshError) => {\r\n                  // 刷新失败，处理等待队列\r\n                  authUtils.processWaitingRequests(refreshError);\r\n                });\r\n            } else {\r\n              reject(error);\r\n            }\r\n          }\r\n        },\r\n        fail: (err) => {\r\n          // 7. 网络错误处理\r\n          let errorMessage = '网络请求失败';\r\n          \r\n          if (err.errMsg) {\r\n            if (err.errMsg.includes('timeout')) {\r\n              errorMessage = '请求超时，请检查网络连接';\r\n            } else if (err.errMsg.includes('fail')) {\r\n              errorMessage = '网络连接失败，请检查网络设置';\r\n            } else {\r\n              errorMessage = `网络请求失败：${err.errMsg}`;\r\n            }\r\n          }\r\n\r\n          // 显示错误提示\r\n          uni.showToast({\r\n            title: errorMessage,\r\n            icon: 'none',\r\n            duration: 2000\r\n          });\r\n\r\n          reject(new Error(errorMessage));\r\n        }\r\n      });\r\n\r\n    } catch (error) {\r\n      // 8. 其他异常处理\r\n      console.error('请求处理异常:', error);\r\n      reject(error);\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Token管理工具 - 增强版本，支持access_token和token_type\r\n */\r\nexport const authUtils = {\r\n  // 设置完整的token信息\r\n  setTokenInfo: (tokenInfo) => {\r\n    const { access_token, token_type = 'Bearer', expires_in } = tokenInfo;\r\n\r\n    if (access_token) {\r\n      uni.setStorageSync('access_token', access_token);\r\n      uni.setStorageSync('token_type', token_type);\r\n\r\n      // 设置过期时间\r\n      if (expires_in) {\r\n        const expireTime = Date.now() + (expires_in * 1000);\r\n        uni.setStorageSync('token_expire_time', expireTime);\r\n      }\r\n\r\n      console.log(`Token信息已保存: ${token_type} ${access_token.substring(0, 10)}...`);\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n\r\n  // 手动设置token到本地存储（向后兼容）\r\n  setToken: (token) => {\r\n    if (token) {\r\n      uni.setStorageSync('access_token', token);\r\n      uni.setStorageSync('token_type', 'Bearer');\r\n      console.log('Token已保存到本地存储');\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n\r\n  // 获取当前token\r\n  getToken: () => {\r\n    return getToken();\r\n  },\r\n\r\n  // 获取token类型\r\n  getTokenType: () => {\r\n    return getTokenType();\r\n  },\r\n\r\n  // 获取完整的Authorization头\r\n  getAuthorizationHeader: () => {\r\n    const token = getToken();\r\n    const tokenType = getTokenType();\r\n    return token ? `${tokenType} ${token}` : null;\r\n  },\r\n\r\n  // 检查token是否过期（增强版本，支持提前刷新和容错处理）\r\n  isTokenExpired: (advanceMinutes = 5) => {\r\n    const expireTime = uni.getStorageSync('token_expire_time');\r\n    // 容错机制：如果过期时间不存在或无效，视为过期\r\n    if (!expireTime || expireTime <= 0) return true;\r\n    \r\n    const currentTime = Date.now();\r\n    const advanceTime = advanceMinutes * 60 * 1000; // 提前N分钟刷新，默认5分钟\r\n    \r\n    return currentTime >= (expireTime - advanceTime);\r\n  },\r\n\r\n  // 清除token\r\n  clearToken: () => {\r\n    const keysToRemove = [\r\n      'token', 'access_token', 'token_type', 'token_expire_time', 'refresh_token',\r\n      'userInfo', 'openid', 'unionid', 'sessionKey', 'wechat_userInfo'\r\n    ];\r\n    keysToRemove.forEach(key => {\r\n      uni.removeStorageSync(key);\r\n    });\r\n    console.log('所有Token信息已清除');\r\n  },\r\n\r\n  // 检查token是否存在且未过期\r\n  hasValidToken: () => {\r\n    const token = getToken();\r\n    return !!token && !authUtils.isTokenExpired();\r\n  },\r\n\r\n  // 检查token是否存在（不检查过期）\r\n  hasToken: () => {\r\n    const token = getToken();\r\n    return !!token;\r\n  },\r\n\r\n  // 获取refresh_token\r\n  getRefreshToken: () => {\r\n    return uni.getStorageSync('refresh_token') || null;\r\n  },\r\n\r\n  // 设置refresh_token\r\n  setRefreshToken: (refreshToken) => {\r\n    if (refreshToken) {\r\n      uni.setStorageSync('refresh_token', refreshToken);\r\n      console.log('Refresh Token已保存');\r\n      return true;\r\n    }\r\n    return false;\r\n  },\r\n\r\n  // Token自动刷新核心函数 - 增强版本\r\n  refreshToken: async () => {\r\n    // 防止并发刷新\r\n    if (isRefreshing) {\r\n      return refreshPromise;\r\n    }\r\n\r\n    const refreshToken = authUtils.getRefreshToken();\r\n    if (!refreshToken) {\r\n      throw new Error('Refresh Token不存在，请重新登录');\r\n    }\r\n\r\n    isRefreshing = true;\r\n    refreshPromise = new Promise(async (resolve, reject) => {\r\n      try {\r\n        console.log('开始刷新Token...');\r\n        \r\n        // 调用刷新API，最多重试2次\r\n        let retryCount = 0;\r\n        const maxRetries = 2;\r\n        \r\n        while (retryCount <= maxRetries) {\r\n          try {\r\n            // 注意：这里不能使用api.wechat.refresh，因为会造成循环依赖\r\n            // Token刷新必须使用原生uni.request避免拦截器循环\r\n            const response = await uni.request({\r\n              url: `${getBaseURL()}/wechat/refresh/`,\r\n              method: 'POST',\r\n              header: {\r\n                'Authorization': `Bearer ${refreshToken}`,\r\n                'Content-Type': 'application/json'\r\n              },\r\n              timeout: getTimeout(),\r\n              data: {\r\n                refresh_token: refreshToken\r\n              }\r\n            });\r\n\r\n            // 检查响应状态\r\n            if (response.statusCode === 200 && response.data) {\r\n              // 支持多种响应格式\r\n              const responseData = response.data.data || response.data;\r\n              const { access_token, token_type, expires_in, refresh_token: newRefreshToken } = responseData;\r\n              \r\n              if (access_token) {\r\n                // 更新token信息\r\n                authUtils.setTokenInfo({\r\n                  access_token,\r\n                  token_type: token_type || 'Bearer',\r\n                  expires_in\r\n                });\r\n\r\n                // 如果返回了新的refresh_token，则更新\r\n                if (newRefreshToken) {\r\n                  authUtils.setRefreshToken(newRefreshToken);\r\n                }\r\n\r\n                console.log('Token刷新成功');\r\n                resolve({\r\n                  access_token,\r\n                  token_type: token_type || 'Bearer',\r\n                  expires_in,\r\n                  refresh_token: newRefreshToken || refreshToken\r\n                });\r\n                return;\r\n              }\r\n            }\r\n\r\n            // 如果是401/403，说明refresh_token无效，不需要重试\r\n            if (response.statusCode === 401 || response.statusCode === 403) {\r\n              throw new Error('Refresh Token已过期，请重新登录');\r\n            }\r\n\r\n            // 其他错误，准备重试\r\n            throw new Error(`刷新失败，状态码: ${response.statusCode}, 响应: ${JSON.stringify(response.data)}`);\r\n\r\n          } catch (error) {\r\n            retryCount++;\r\n            \r\n            if (retryCount > maxRetries) {\r\n              throw error;\r\n            }\r\n\r\n            // 重试前等待1秒\r\n            await new Promise(resolve => setTimeout(resolve, 1000));\r\n            console.log(`Token刷新重试 ${retryCount}/${maxRetries}`);\r\n          }\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('Token刷新失败:', error.message);\r\n        \r\n        // 刷新失败，清除所有token相关数据\r\n        authUtils.clearToken();\r\n        \r\n        // 显示错误提示（防止重复提示）\r\n        if (!authUtils._showingLogoutToast) {\r\n          authUtils._showingLogoutToast = true;\r\n          uni.showToast({\r\n            title: '登录已过期，请重新登录',\r\n            icon: 'none',\r\n            duration: 2000\r\n          });\r\n\r\n          // 重置提示状态\r\n          setTimeout(() => {\r\n            authUtils._showingLogoutToast = false;\r\n          }, 3000);\r\n        }\r\n\r\n        // 跳转到登录页（防止重复跳转）\r\n        if (!authUtils._navigatingToLogin) {\r\n          authUtils._navigatingToLogin = true;\r\n          setTimeout(() => {\r\n            uni.navigateTo({\r\n              url: '/pages/login/login',\r\n              complete: () => {\r\n                authUtils._navigatingToLogin = false;\r\n              }\r\n            });\r\n          }, 2000);\r\n        }\r\n\r\n        reject(error);\r\n      } finally {\r\n        isRefreshing = false;\r\n        refreshPromise = null;\r\n      }\r\n    });\r\n\r\n    return refreshPromise;\r\n  },\r\n\r\n  // 添加请求到等待队列\r\n  addRequestToQueue: (config, resolve, reject) => {\r\n    waitingRequests.push({ config, resolve, reject });\r\n  },\r\n\r\n  // 处理等待队列中的请求\r\n  processWaitingRequests: (error = null) => {\r\n    waitingRequests.forEach(({ config, resolve, reject }) => {\r\n      if (error) {\r\n        reject(error);\r\n      } else {\r\n        // 重新发起请求\r\n        request(config).then(resolve).catch(reject);\r\n      }\r\n    });\r\n    // 清空队列\r\n    waitingRequests.length = 0;\r\n  },\r\n\r\n  // 优化的token有效性检查（带缓存）\r\n  checkTokenValidityWithCache: () => {\r\n    const now = Date.now();\r\n    \r\n    // 防抖：5秒内最多检查一次\r\n    if (now - lastTokenCheck < TOKEN_CHECK_INTERVAL) {\r\n      return true; // 假设在短时间内token状态没有变化\r\n    }\r\n    \r\n    lastTokenCheck = now;\r\n    const hasToken = authUtils.hasToken();\r\n    const isExpired = authUtils.isTokenExpired();\r\n    \r\n    return hasToken && !isExpired;\r\n  }\r\n};\r\n"], "names": ["uni", "getBaseURL", "getTimeout", "resolve"], "mappings": ";;;AAIA,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,MAAM,kBAAkB,CAAA;AAIxB,IAAI,iBAAiB;AACrB,MAAM,uBAAuB;AAMtB,MAAM,WAAW,MAAM;;AAE5B,MAAI,QAAQA,cAAAA,MAAI,eAAe,cAAc;AAG7C,MAAI,CAAC,OAAO;AACV,YAAQA,cAAG,MAAC,eAAe,OAAO;AAAA,EACnC;AAGD,MAAI,CAAC,OAAO;AACV,QAAI;AACF,YAAM,QAAQ;AACd,UAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,cAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,kBAAQ,iBAAY,YAAZ,mBAAqB,UAAS;AAGtC,YAAI,OAAO;AACTA,wBAAAA,MAAI,eAAe,gBAAgB,KAAK;AACxCA,wBAAAA,8CAAY,uBAAuB;AAAA,QACpC;AAAA,MACF;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAA,MAAA,MAAA,OAAA,2BAAY,kBAAkB,KAAK;AAAA,IACpC;AAAA,EACF;AAED,SAAO;AACT;AAMO,MAAM,eAAe,MAAM;AAChC,SAAOA,oBAAI,eAAe,YAAY,KAAK;AAC7C;AAMA,MAAM,eAAe;AAAA,EACnB;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AACF;AAKA,MAAM,iBAAiB,CAAC,QAAQ;AAC9B,SAAO,aAAa,KAAK,aAAW;AAElC,WAAO,QAAQ,WAAW,IAAI,WAAW,OAAO;AAAA,EACpD,CAAG;AACH;AAMO,MAAM,qBAAqB,CAAC,WAAW;AAE5C,QAAM,WAAW,OAAO,YAAY,eAAe,OAAO,GAAG;AAE7D,MAAI,UAAU;AACZA,gEAAY,MAAM,OAAO,GAAG,oBAAoB;AAChD,WAAO;AAAA,EACR;AAGD,QAAM,QAAQ;AACd,QAAM,YAAY;AAElB,MAAI,OAAO;AACT,WAAO,SAAS;AAAA,MACd,GAAG,OAAO;AAAA,MACV,iBAAiB,GAAG,SAAS,IAAI,KAAK;AAAA,IAC5C;AACIA,kBAAAA,MAAA,MAAA,OAAA,4BAAY,yBAAyB,SAAS,IAAI,MAAM,UAAU,GAAG,EAAE,CAAC,KAAK;AAAA,EACjF,OAAS;AACLA,wBAAa,MAAA,QAAA,4BAAA,MAAM,OAAO,GAAG,gCAAgC;AAAA,EAC9D;AAED,SAAO;AACT;AAMO,MAAM,sBAAsB,CAAC,aAAa;AAE/C,MAAI,SAAS,eAAe,KAAK;AAE/BA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,IACZ,CAAK;AAGD,UAAM,eAAe;AAAA,MACnB;AAAA,MAAS;AAAA,MAAgB;AAAA,MAAc;AAAA,MAAqB;AAAA,MAC5D;AAAA,MAAY;AAAA,MAAU;AAAA,MAAW;AAAA,MAAc;AAAA,IACrD;AACI,iBAAa,QAAQ,SAAO;AAC1BA,0BAAI,kBAAkB,GAAG;AAAA,IAC/B,CAAK;AAGD,eAAW,MAAM;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACb,CAAO;AAAA,IACF,GAAE,IAAI;AAEP,WAAO,QAAQ,OAAO,IAAI,MAAM,eAAe,CAAC;AAAA,EACjD;AAGD,MAAI,SAAS,eAAe,KAAK;AAC/BA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO,SAAS,KAAK,WAAW;AAAA,MAChC,MAAM;AAAA,IACZ,CAAK;AACD,WAAO,QAAQ,OAAO,IAAI,MAAM,SAAS,KAAK,WAAW,MAAM,CAAC;AAAA,EACjE;AAED,SAAO,SAAS;AAClB;AAQY,MAAC,gBAAgB,CAAC,iBAAiB,OAAO;AACpD,SAAO,CAAC,YAAY;AAClB,WAAO,QAAQ;AAAA,MACb,GAAG;AAAA,MACH,GAAG;AAAA,IACT,CAAK;AAAA,EACL;AACA;AAMY,MAAC,UAAU,CAAC,YAAY;AAClC,SAAO,IAAI,QAAQ,OAAO,SAAS,WAAW;AAC5C,QAAI;AAEF,YAAM,eAAe,UAAU;AAG/B,UAAI,CAAC,gBAAgB,UAAU,mBAAmB;AAChD,YAAI,cAAc;AAEhB,oBAAU,kBAAkB,SAAS,SAAS,MAAM;AACpD;AAAA,QACD;AAED,YAAI;AAEF,gBAAM,UAAU;AAEhB,oBAAU,uBAAsB;AAAA,QACjC,SAAQ,cAAc;AAErB,oBAAU,uBAAuB,YAAY;AAC7C,iBAAO,YAAY;AACnB;AAAA,QACD;AAAA,MACF;AAGD,YAAM,SAAS,mBAAmB,OAAO;AAGzCA,oBAAAA,MAAI,QAAQ;AAAA,QACV,GAAG;AAAA,QACH,KAAK,GAAGC,WAAU,WAAA,CAAE,GAAG,OAAO,GAAG;AAAA,QACjC,SAASC,WAAAA,WAAY;AAAA,QACrB,SAAS,CAAC,QAAQ;AAChB,cAAI;AAEF,kBAAM,WAAW,oBAAoB,GAAG;AACxC,oBAAQ,QAAQ;AAAA,UACjB,SAAQ,OAAO;AAEd,gBAAI,IAAI,eAAe,OAAO,CAAC,gBAAgB,UAAU,mBAAmB;AAE1E,wBAAU,kBAAkB,SAAS,SAAS,MAAM;AAEpD,wBAAU,aAAc,EACrB,KAAK,MAAM;AAEV,0BAAU,uBAAsB;AAAA,cAClD,CAAiB,EACA,MAAM,CAAC,iBAAiB;AAEvB,0BAAU,uBAAuB,YAAY;AAAA,cAC/D,CAAiB;AAAA,YACjB,OAAmB;AACL,qBAAO,KAAK;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,QACD,MAAM,CAAC,QAAQ;AAEb,cAAI,eAAe;AAEnB,cAAI,IAAI,QAAQ;AACd,gBAAI,IAAI,OAAO,SAAS,SAAS,GAAG;AAClC,6BAAe;AAAA,YAChB,WAAU,IAAI,OAAO,SAAS,MAAM,GAAG;AACtC,6BAAe;AAAA,YAC7B,OAAmB;AACL,6BAAe,UAAU,IAAI,MAAM;AAAA,YACpC;AAAA,UACF;AAGDF,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACtB,CAAW;AAED,iBAAO,IAAI,MAAM,YAAY,CAAC;AAAA,QAC/B;AAAA,MACT,CAAO;AAAA,IAEF,SAAQ,OAAO;AAEdA,qEAAc,WAAW,KAAK;AAC9B,aAAO,KAAK;AAAA,IACb;AAAA,EACL,CAAG;AACH;AAKY,MAAC,YAAY;AAAA;AAAA,EAEvB,cAAc,CAAC,cAAc;AAC3B,UAAM,EAAE,cAAc,aAAa,UAAU,WAAU,IAAK;AAE5D,QAAI,cAAc;AAChBA,oBAAAA,MAAI,eAAe,gBAAgB,YAAY;AAC/CA,oBAAAA,MAAI,eAAe,cAAc,UAAU;AAG3C,UAAI,YAAY;AACd,cAAM,aAAa,KAAK,IAAK,IAAI,aAAa;AAC9CA,sBAAAA,MAAI,eAAe,qBAAqB,UAAU;AAAA,MACnD;AAEDA,oBAAAA,MAAY,MAAA,OAAA,4BAAA,eAAe,UAAU,IAAI,aAAa,UAAU,GAAG,EAAE,CAAC,KAAK;AAC3E,aAAO;AAAA,IACR;AACD,WAAO;AAAA,EACR;AAAA;AAAA,EAGD,UAAU,CAAC,UAAU;AACnB,QAAI,OAAO;AACTA,oBAAAA,MAAI,eAAe,gBAAgB,KAAK;AACxCA,oBAAAA,MAAI,eAAe,cAAc,QAAQ;AACzCA,oBAAAA,MAAY,MAAA,OAAA,4BAAA,eAAe;AAC3B,aAAO;AAAA,IACR;AACD,WAAO;AAAA,EACR;AAAA;AAAA,EAGD,UAAU,MAAM;AACd,WAAO,SAAQ;AAAA,EAChB;AAAA;AAAA,EAGD,cAAc,MAAM;AAClB,WAAO,aAAY;AAAA,EACpB;AAAA;AAAA,EAGD,wBAAwB,MAAM;AAC5B,UAAM,QAAQ;AACd,UAAM,YAAY;AAClB,WAAO,QAAQ,GAAG,SAAS,IAAI,KAAK,KAAK;AAAA,EAC1C;AAAA;AAAA,EAGD,gBAAgB,CAAC,iBAAiB,MAAM;AACtC,UAAM,aAAaA,cAAAA,MAAI,eAAe,mBAAmB;AAEzD,QAAI,CAAC,cAAc,cAAc;AAAG,aAAO;AAE3C,UAAM,cAAc,KAAK;AACzB,UAAM,cAAc,iBAAiB,KAAK;AAE1C,WAAO,eAAgB,aAAa;AAAA,EACrC;AAAA;AAAA,EAGD,YAAY,MAAM;AAChB,UAAM,eAAe;AAAA,MACnB;AAAA,MAAS;AAAA,MAAgB;AAAA,MAAc;AAAA,MAAqB;AAAA,MAC5D;AAAA,MAAY;AAAA,MAAU;AAAA,MAAW;AAAA,MAAc;AAAA,IACrD;AACI,iBAAa,QAAQ,SAAO;AAC1BA,0BAAI,kBAAkB,GAAG;AAAA,IAC/B,CAAK;AACDA,kBAAAA,MAAA,MAAA,OAAA,4BAAY,cAAc;AAAA,EAC3B;AAAA;AAAA,EAGD,eAAe,MAAM;AACnB,UAAM,QAAQ;AACd,WAAO,CAAC,CAAC,SAAS,CAAC,UAAU,eAAc;AAAA,EAC5C;AAAA;AAAA,EAGD,UAAU,MAAM;AACd,UAAM,QAAQ;AACd,WAAO,CAAC,CAAC;AAAA,EACV;AAAA;AAAA,EAGD,iBAAiB,MAAM;AACrB,WAAOA,oBAAI,eAAe,eAAe,KAAK;AAAA,EAC/C;AAAA;AAAA,EAGD,iBAAiB,CAAC,iBAAiB;AACjC,QAAI,cAAc;AAChBA,oBAAAA,MAAI,eAAe,iBAAiB,YAAY;AAChDA,oBAAAA,MAAY,MAAA,OAAA,4BAAA,kBAAkB;AAC9B,aAAO;AAAA,IACR;AACD,WAAO;AAAA,EACR;AAAA;AAAA,EAGD,cAAc,YAAY;AAExB,QAAI,cAAc;AAChB,aAAO;AAAA,IACR;AAED,UAAM,eAAe,UAAU;AAC/B,QAAI,CAAC,cAAc;AACjB,YAAM,IAAI,MAAM,wBAAwB;AAAA,IACzC;AAED,mBAAe;AACf,qBAAiB,IAAI,QAAQ,OAAO,SAAS,WAAW;AACtD,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,4BAAY,cAAc;AAG1B,YAAI,aAAa;AACjB,cAAM,aAAa;AAEnB,eAAO,cAAc,YAAY;AAC/B,cAAI;AAGF,kBAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,cACjC,KAAK,GAAGC,sBAAY,CAAA;AAAA,cACpB,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,iBAAiB,UAAU,YAAY;AAAA,gBACvC,gBAAgB;AAAA,cACjB;AAAA,cACD,SAASC,WAAAA,WAAY;AAAA,cACrB,MAAM;AAAA,gBACJ,eAAe;AAAA,cAChB;AAAA,YACf,CAAa;AAGD,gBAAI,SAAS,eAAe,OAAO,SAAS,MAAM;AAEhD,oBAAM,eAAe,SAAS,KAAK,QAAQ,SAAS;AACpD,oBAAM,EAAE,cAAc,YAAY,YAAY,eAAe,gBAAiB,IAAG;AAEjF,kBAAI,cAAc;AAEhB,0BAAU,aAAa;AAAA,kBACrB;AAAA,kBACA,YAAY,cAAc;AAAA,kBAC1B;AAAA,gBAClB,CAAiB;AAGD,oBAAI,iBAAiB;AACnB,4BAAU,gBAAgB,eAAe;AAAA,gBAC1C;AAEDF,8BAAAA,MAAA,MAAA,OAAA,4BAAY,WAAW;AACvB,wBAAQ;AAAA,kBACN;AAAA,kBACA,YAAY,cAAc;AAAA,kBAC1B;AAAA,kBACA,eAAe,mBAAmB;AAAA,gBACpD,CAAiB;AACD;AAAA,cACD;AAAA,YACF;AAGD,gBAAI,SAAS,eAAe,OAAO,SAAS,eAAe,KAAK;AAC9D,oBAAM,IAAI,MAAM,wBAAwB;AAAA,YACzC;AAGD,kBAAM,IAAI,MAAM,aAAa,SAAS,UAAU,SAAS,KAAK,UAAU,SAAS,IAAI,CAAC,EAAE;AAAA,UAEzF,SAAQ,OAAO;AACd;AAEA,gBAAI,aAAa,YAAY;AAC3B,oBAAM;AAAA,YACP;AAGD,kBAAM,IAAI,QAAQ,CAAAG,aAAW,WAAWA,UAAS,GAAI,CAAC;AACtDH,gCAAA,MAAA,OAAA,4BAAY,aAAa,UAAU,IAAI,UAAU,EAAE;AAAA,UACpD;AAAA,QACF;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,4BAAc,cAAc,MAAM,OAAO;AAGzC,kBAAU,WAAU;AAGpB,YAAI,CAAC,UAAU,qBAAqB;AAClC,oBAAU,sBAAsB;AAChCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACtB,CAAW;AAGD,qBAAW,MAAM;AACf,sBAAU,sBAAsB;AAAA,UACjC,GAAE,GAAI;AAAA,QACR;AAGD,YAAI,CAAC,UAAU,oBAAoB;AACjC,oBAAU,qBAAqB;AAC/B,qBAAW,MAAM;AACfA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK;AAAA,cACL,UAAU,MAAM;AACd,0BAAU,qBAAqB;AAAA,cAChC;AAAA,YACf,CAAa;AAAA,UACF,GAAE,GAAI;AAAA,QACR;AAED,eAAO,KAAK;AAAA,MACpB,UAAgB;AACR,uBAAe;AACf,yBAAiB;AAAA,MAClB;AAAA,IACP,CAAK;AAED,WAAO;AAAA,EACR;AAAA;AAAA,EAGD,mBAAmB,CAAC,QAAQ,SAAS,WAAW;AAC9C,oBAAgB,KAAK,EAAE,QAAQ,SAAS,OAAQ,CAAA;AAAA,EACjD;AAAA;AAAA,EAGD,wBAAwB,CAAC,QAAQ,SAAS;AACxC,oBAAgB,QAAQ,CAAC,EAAE,QAAQ,SAAS,OAAM,MAAO;AACvD,UAAI,OAAO;AACT,eAAO,KAAK;AAAA,MACpB,OAAa;AAEL,gBAAQ,MAAM,EAAE,KAAK,OAAO,EAAE,MAAM,MAAM;AAAA,MAC3C;AAAA,IACP,CAAK;AAED,oBAAgB,SAAS;AAAA,EAC1B;AAAA;AAAA,EAGD,6BAA6B,MAAM;AACjC,UAAM,MAAM,KAAK;AAGjB,QAAI,MAAM,iBAAiB,sBAAsB;AAC/C,aAAO;AAAA,IACR;AAED,qBAAiB;AACjB,UAAM,WAAW,UAAU;AAC3B,UAAM,YAAY,UAAU;AAE5B,WAAO,YAAY,CAAC;AAAA,EACrB;AACH;;;;"}